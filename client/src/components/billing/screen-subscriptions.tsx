import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { useBilling } from "@/hooks/use-billing";
import { format } from "date-fns";

interface ScreenSubscriptionsProps {
  teamId: string;
  teamName: string;
}

export function ScreenSubscriptions({ teamId, teamName }: ScreenSubscriptionsProps) {
  const { 
    screenSubscriptions, 
    subscriptionsLoading, 
    calculateNextBillingDate,
    generateInvoiceId 
  } = useBilling(teamId);

  if (subscriptionsLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-muted-foreground">Loading screen subscriptions...</div>
      </div>
    );
  }

  if (screenSubscriptions.length === 0) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-muted-foreground">No screen subscriptions found.</div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Screen Name</TableHead>
              <TableHead>Location</TableHead>
              <TableHead>Screen Code</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Billing Cycle</TableHead>
              <TableHead>Invoice ID</TableHead>
              <TableHead>Next Billing Date</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {screenSubscriptions.map((subscription, index) => {
              const nextBillingDate = calculateNextBillingDate(
                subscription.invoiceDate, 
                subscription.billingCycle || ""
              );
              
              return (
                <TableRow key={`${subscription.screenCode}-${index}`}>
                  <TableCell className="font-medium">
                    {subscription.screenName}
                  </TableCell>
                  <TableCell>
                    {subscription.screenLocation || "N/A"}
                  </TableCell>
                  <TableCell>
                    <code className="bg-muted px-2 py-1 rounded text-sm">
                      {subscription.screenCode}
                    </code>
                  </TableCell>
                  <TableCell>
                    <Badge 
                      variant={
                        subscription.subscriptionStatus === "active" 
                          ? "default" 
                          : subscription.subscriptionStatus === "trial"
                          ? "secondary"
                          : "outline"
                      }
                    >
                      {subscription.subscriptionStatus}
                    </Badge>
                  </TableCell>
                  <TableCell className="capitalize">
                    {subscription.billingCycle || "N/A"}
                  </TableCell>
                  <TableCell>
                    {generateInvoiceId(teamName, subscription.invoiceId)}
                  </TableCell>
                  <TableCell>
                    {nextBillingDate ? format(nextBillingDate, "MMM dd, yyyy") : "N/A"}
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
