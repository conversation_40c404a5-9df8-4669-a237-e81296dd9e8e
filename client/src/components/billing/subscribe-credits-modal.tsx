import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { useBilling, type AvailableScreen } from "@/hooks/use-billing";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";

interface SubscribeCreditsModalProps {
  isOpen: boolean;
  onClose: () => void;
  teamId: string;
  invoiceId: number;
  availableCredits: number;
  invoiceDate: Date | null;
  billingCycle: string;
}

export function SubscribeCreditsModal({
  isOpen,
  onClose,
  teamId,
  invoiceId,
  availableCredits,
  invoiceDate,
  billingCycle,
}: SubscribeCreditsModalProps) {
  const { toast } = useToast();
  const {
    availableScreens,
    availableScreensLoading,
    bulkUpdateScreenRegistrations,
    isBulkUpdating,
  } = useBilling(teamId);

  const [selectedScreens, setSelectedScreens] = useState<Set<string>>(new Set());

  // Reset selected screens when modal opens
  useEffect(() => {
    if (isOpen) {
      setSelectedScreens(new Set());
    }
  }, [isOpen]);

  const handleScreenToggle = (screenId: string, checked: boolean) => {
    const newSelected = new Set(selectedScreens);
    if (checked) {
      if (newSelected.size < availableCredits) {
        newSelected.add(screenId);
      } else {
        toast({
          title: "Limit Reached",
          description: `You can only select up to ${availableCredits} screens.`,
          variant: "destructive",
        });
        return;
      }
    } else {
      newSelected.delete(screenId);
    }
    setSelectedScreens(newSelected);
  };

  const handleSubscribe = () => {
    if (selectedScreens.size === 0) {
      toast({
        title: "No Screens Selected",
        description: "Please select at least one screen to subscribe.",
        variant: "destructive",
      });
      return;
    }

    if (!invoiceDate) {
      toast({
        title: "Invalid Invoice",
        description: "Invoice date is required.",
        variant: "destructive",
      });
      return;
    }

    // Calculate new trial end date based on invoice date + billing cycle
    const trialEndsAt = new Date(invoiceDate);
    switch (billingCycle?.toLowerCase()) {
      case 'monthly':
        trialEndsAt.setDate(trialEndsAt.getDate() + 30);
        break;
      case '6 months':
        trialEndsAt.setDate(trialEndsAt.getDate() + 180);
        break;
      case 'yearly':
        trialEndsAt.setDate(trialEndsAt.getDate() + 365);
        break;
      default:
        trialEndsAt.setDate(trialEndsAt.getDate() + 30); // Default to monthly
    }

    // Prepare updates for selected screens
    const updates = Array.from(selectedScreens).map(screenId => ({
      screenId,
      trialEndsAt,
      subscriptionStatus: 'active',
      billingCycle,
      invoiceId,
    }));

    bulkUpdateScreenRegistrations(updates);
    onClose();
  };

  if (availableScreensLoading) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Subscribe Available Credits</DialogTitle>
            <DialogDescription>
              Loading available screens...
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center justify-center py-8">
            <div className="text-muted-foreground">Loading...</div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>Subscribe Available Credits</DialogTitle>
          <DialogDescription>
            You have {availableCredits} available credits. Select screens to subscribe.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {availableScreens.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-muted-foreground">No screens available for subscription.</div>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">Subscribe</TableHead>
                    <TableHead>Screen Name</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Screen Code</TableHead>
                    <TableHead>Subscription End Date</TableHead>
                    <TableHead>Billing Cycle</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {availableScreens.map((screen) => (
                    <TableRow key={screen.screenId}>
                      <TableCell>
                        <Checkbox
                          checked={selectedScreens.has(screen.screenId)}
                          onCheckedChange={(checked) =>
                            handleScreenToggle(screen.screenId, checked as boolean)
                          }
                          disabled={
                            !selectedScreens.has(screen.screenId) &&
                            selectedScreens.size >= availableCredits
                          }
                        />
                      </TableCell>
                      <TableCell className="font-medium">{screen.screenName}</TableCell>
                      <TableCell>{screen.screenLocation || "N/A"}</TableCell>
                      <TableCell>{screen.screenCode}</TableCell>
                      <TableCell>
                        {screen.trialEndsAt
                          ? format(screen.trialEndsAt, "MMM dd, yyyy")
                          : "N/A"}
                      </TableCell>
                      <TableCell className="capitalize">
                        {screen.billingCycle || "N/A"}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            screen.subscriptionStatus === "trial"
                              ? "secondary"
                              : "destructive"
                          }
                        >
                          {screen.subscriptionStatus}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          <div className="text-sm text-muted-foreground">
            Selected: {selectedScreens.size} / {availableCredits} available credits
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSubscribe}
            disabled={selectedScreens.size === 0 || isBulkUpdating}
          >
            {isBulkUpdating ? "Subscribing..." : "Subscribe Credits"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
