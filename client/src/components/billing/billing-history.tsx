import React, { useState, useMemo } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { useBilling } from "@/hooks/use-billing";
import { format } from "date-fns";
import { FileText, RefreshCw, Trash2, CreditCard, Search } from "lucide-react";
import { SubscribeCreditsModal } from "./subscribe-credits-modal";

interface BillingHistoryProps {
  teamId: string;
  teamName: string;
}

export function BillingHistory({ teamId, teamName }: BillingHistoryProps) {
  const {
    invoices,
    invoicesLoading,
    updateInvoicePayment,
    deleteInvoice,
    isUpdatingPayment,
    isDeletingInvoice,
    calculateNextBillingDate,
    generateInvoiceId
  } = useBilling(teamId);

  const [searchTerm, setSearchTerm] = useState("");
  const [subscribeCreditsModal, setSubscribeCreditsModal] = useState<{
    isOpen: boolean;
    invoiceId: number;
    availableCredits: number;
    invoiceDate: Date | null;
    billingCycle: string;
  }>({
    isOpen: false,
    invoiceId: 0,
    availableCredits: 0,
    invoiceDate: null,
    billingCycle: '',
  });

  // Filter invoices based on search term
  const filteredInvoices = useMemo(() => {
    if (!searchTerm.trim()) return invoices;

    const searchLower = searchTerm.toLowerCase();
    return invoices.filter(invoice => {
      const invoiceId = generateInvoiceId(teamName, invoice.invoiceId).toLowerCase();
      const billingCycle = (invoice.billingCycle || '').toLowerCase();
      const status = (invoice.paidAt ? 'paid' : 'unpaid').toLowerCase();
      const amount = `$${(invoice.totalAmount || 0).toFixed(2)}`;

      return (
        invoiceId.includes(searchLower) ||
        billingCycle.includes(searchLower) ||
        status.includes(searchLower) ||
        amount.includes(searchLower)
      );
    });
  }, [invoices, searchTerm, teamName, generateInvoiceId]);

  const handleRetryPayment = (invoiceId: number) => {
    updateInvoicePayment({
      invoiceId: invoiceId.toString(),
      paidAt: new Date(),
    });
  };

  const handleDeleteInvoice = (invoiceId: number) => {
    if (confirm("Are you sure you want to delete this invoice?")) {
      deleteInvoice(invoiceId.toString());
    }
  };

  const handlePrintInvoice = (invoiceId: number) => {
    // In a real application, this would generate and download a PDF invoice
    alert(`Print invoice functionality would be implemented here for invoice ${generateInvoiceId(teamName, invoiceId)}`);
  };

  const handleSubscribeCredits = (invoice: any) => {
    const availableCredits = (invoice.qty || 0) - (invoice.creditUsed || 0);
    setSubscribeCreditsModal({
      isOpen: true,
      invoiceId: invoice.invoiceId,
      availableCredits,
      invoiceDate: invoice.invoiceDate,
      billingCycle: invoice.billingCycle || '',
    });
  };

  const closeSubscribeCreditsModal = () => {
    setSubscribeCreditsModal(prev => ({ ...prev, isOpen: false }));
  };

  return (
    <div className="space-y-4">
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          placeholder="Search invoices by ID, billing cycle, status, or amount..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Invoice ID</TableHead>
              <TableHead>Invoice Date</TableHead>
              <TableHead>Billing Cycle</TableHead>
              <TableHead>Quantity</TableHead>
              <TableHead>Total Amount</TableHead>
              <TableHead>Next Billing Date</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {invoicesLoading ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  <RefreshCw className="h-4 w-4 animate-spin mx-auto mb-2" />
                  Loading invoices...
                </TableCell>
              </TableRow>
            ) : filteredInvoices.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                  <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  {searchTerm.trim() ? `No invoices found matching "${searchTerm}"` : "No invoices found"}
                </TableCell>
              </TableRow>
            ) : (
              filteredInvoices.map((invoice) => {
              const nextBillingDate = calculateNextBillingDate(invoice.invoiceDate, invoice.billingCycle || "");
              const isPaid = !!invoice.paidAt;
              const creditUsed = invoice.creditUsed || 0;
              const availableCredits = (invoice.qty || 0) - creditUsed;
              const hasAvailableCredits = availableCredits > 0;

              return (
                <TableRow key={invoice.invoiceId}>
                  <TableCell className="font-medium">
                    {generateInvoiceId(teamName, invoice.invoiceId)}
                  </TableCell>
                  <TableCell>
                    {invoice.invoiceDate ? format(invoice.invoiceDate, "MMM dd, yyyy") : "N/A"}
                  </TableCell>
                  <TableCell className="capitalize">
                    {invoice.billingCycle || "N/A"}
                  </TableCell>
                  <TableCell>{invoice.qty || 0}</TableCell>
                  <TableCell>
                    ${(invoice.totalAmount || 0).toFixed(2)}
                  </TableCell>
                  <TableCell>
                    {nextBillingDate ? format(nextBillingDate, "MMM dd, yyyy") : "N/A"}
                  </TableCell>
                  <TableCell>
                    <Badge variant={isPaid ? "default" : "destructive"}>
                      {isPaid ? "Paid" : "Unpaid"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {!isPaid ? (
                        <>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleRetryPayment(invoice.invoiceId)}
                            disabled={isUpdatingPayment}
                          >
                            <RefreshCw className="h-4 w-4 mr-1" />
                            Retry Payment
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDeleteInvoice(invoice.invoiceId)}
                            disabled={isDeletingInvoice}
                          >
                            <Trash2 className="h-4 w-4 mr-1" />
                            Delete
                          </Button>
                        </>
                      ) : (
                        <>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handlePrintInvoice(invoice.invoiceId)}
                          >
                            <FileText className="h-4 w-4 mr-1" />
                            Print Invoice
                          </Button>
                          {hasAvailableCredits && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleSubscribeCredits(invoice)}
                            >
                              <CreditCard className="h-4 w-4 mr-1" />
                              Subscribe Credits
                            </Button>
                          )}
                        </>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>

      <SubscribeCreditsModal
        isOpen={subscribeCreditsModal.isOpen}
        onClose={closeSubscribeCreditsModal}
        teamId={teamId}
        invoiceId={subscribeCreditsModal.invoiceId}
        availableCredits={subscribeCreditsModal.availableCredits}
        invoiceDate={subscribeCreditsModal.invoiceDate}
        billingCycle={subscribeCreditsModal.billingCycle}
      />
    </div>
  );
}
